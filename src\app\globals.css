@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --accent: oklch(0.97 0.001 106.424);
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.147 0.004 49.25);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.147 0.004 49.25);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.147 0.004 49.25);
  --primary: oklch(0.216 0.006 56.043);
  --primary-foreground: oklch(0.985 0.001 106.423);
  --secondary: oklch(0.97 0.001 106.424);
  --secondary-foreground: oklch(0.216 0.006 56.043);
  --muted: oklch(0.97 0.001 106.424);
  --muted-foreground: oklch(0.553 0.013 58.071);
  --accent-foreground: oklch(0.216 0.006 56.043);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.923 0.003 48.717);
  --input: oklch(0.923 0.003 48.717);
  --ring: oklch(0.709 0.01 56.259);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.001 106.423);
  --sidebar-foreground: oklch(0.147 0.004 49.25);
  --sidebar-primary: oklch(0.216 0.006 56.043);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.97 0.001 106.424);
  --sidebar-accent-foreground: oklch(0.216 0.006 56.043);
  --sidebar-border: oklch(0.923 0.003 48.717);
  --sidebar-ring: oklch(0.709 0.01 56.259);
}

.dark {
  --background: oklch(0.147 0.004 49.25);
  --foreground: oklch(0.985 0.001 106.423);
  --accent: oklch(0.268 0.007 34.298);
  --card: oklch(0.216 0.006 56.043);
  --card-foreground: oklch(0.985 0.001 106.423);
  --popover: oklch(0.216 0.006 56.043);
  --popover-foreground: oklch(0.985 0.001 106.423);
  --primary: oklch(0.923 0.003 48.717);
  --primary-foreground: oklch(0.216 0.006 56.043);
  --secondary: oklch(0.268 0.007 34.298);
  --secondary-foreground: oklch(0.985 0.001 106.423);
  --muted: oklch(0.268 0.007 34.298);
  --muted-foreground: oklch(0.709 0.01 56.259);
  --accent-foreground: oklch(0.985 0.001 106.423);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.553 0.013 58.071);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.216 0.006 56.043);
  --sidebar-foreground: oklch(0.985 0.001 106.423);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.268 0.007 34.298);
  --sidebar-accent-foreground: oklch(0.985 0.001 106.423);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.553 0.013 58.071);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  min-height: 100%;
}

/* Prevent scroll issues */
* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #22c55e;
}

/* Animation classes for GSAP */
.fade-in {
  opacity: 0;
  transform: translateY(50px);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
}

.parallax-slow {
  will-change: transform;
}

.parallax-fast {
  will-change: transform;
}

/* Mobile menu animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-enter {
  animation: slideInFromTop 0.3s ease-out;
}

/* Music Player Animations */
@keyframes musicSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes musicSpin {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes musicPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes musicWave {
  0%, 100% {
    transform: scaleY(1);
  }
  25% {
    transform: scaleY(1.2);
  }
  50% {
    transform: scaleY(0.8);
  }
  75% {
    transform: scaleY(1.1);
  }
}

.music-spin {
  animation: musicSpin 3s linear infinite;
  transform-origin: center center;
  -webkit-animation: musicSpin 3s linear infinite; /* Safari/iOS support */
}

.music-pulse {
  animation: musicPulse 2s ease-in-out infinite;
}

.music-wave {
  animation: musicWave 1.5s ease-in-out infinite;
}

/* Floating music button hover effect */
.music-player-container:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

/* Mobile Bottom Navigation Glassmorphism */
.mobile-nav-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .mobile-nav-glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Rotating border animation for active music */
@keyframes rotateBorder {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.rotating-border {
  animation: rotateBorder 3s linear infinite;
}

/* Pulsing glow effect */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Music-Reactive Classes */
@keyframes musicReactiveFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes musicReactiveRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes musicReactiveScale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes musicReactiveGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.6), 0 0 30px rgba(34, 197, 94, 0.4);
  }
}

@keyframes musicReactiveColorShift {
  0% {
    filter: hue-rotate(0deg);
  }
  25% {
    filter: hue-rotate(90deg);
  }
  50% {
    filter: hue-rotate(180deg);
  }
  75% {
    filter: hue-rotate(270deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}

@keyframes musicReactiveBorderPulse {
  0%, 100% {
    border-color: rgba(34, 197, 94, 0.3);
    border-width: 1px;
  }
  50% {
    border-color: rgba(34, 197, 94, 0.8);
    border-width: 2px;
  }
}

@keyframes musicReactiveTextGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    text-shadow: 0 0 15px rgba(34, 197, 94, 0.8), 0 0 25px rgba(34, 197, 94, 0.5);
  }
}

/* Music-Reactive Animation Classes */
.music-reactive-float {
  animation: musicReactiveFloat 2s ease-in-out infinite;
}

.music-reactive-rotate {
  animation: musicReactiveRotate 8s linear infinite;
}

.music-reactive-scale {
  animation: musicReactiveScale 1.5s ease-in-out infinite;
}

.music-reactive-glow {
  animation: musicReactiveGlow 2s ease-in-out infinite;
}

.music-reactive-color {
  animation: musicReactiveColorShift 6s linear infinite;
}

.music-reactive-border {
  animation: musicReactiveBorderPulse 2s ease-in-out infinite;
  border: 1px solid transparent;
}

.music-reactive-text-glow {
  animation: musicReactiveTextGlow 2s ease-in-out infinite;
}

/* Intensity-based classes */
.music-intensity-low {
  animation-duration: 3s;
  opacity: 0.7;
}

.music-intensity-medium {
  animation-duration: 2s;
  opacity: 0.85;
}

.music-intensity-high {
  animation-duration: 1s;
  opacity: 1;
}

/* Combined effects for professional look */
.music-reactive-card {
  transition: all 0.3s ease;
}

.music-reactive-card.music-active {
  animation: musicReactiveScale 2s ease-in-out infinite, musicReactiveGlow 2s ease-in-out infinite;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.music-reactive-nav {
  transition: all 0.3s ease;
}

.music-reactive-nav.music-active {
  animation: musicReactiveGlow 3s ease-in-out infinite;
  backdrop-filter: blur(10px) saturate(1.2);
}

.music-reactive-logo {
  transition: all 0.3s ease;
}

.music-reactive-logo.music-active {
  animation: musicReactiveFloat 3s ease-in-out infinite, musicReactiveGlow 3s ease-in-out infinite;
}

.music-reactive-text {
  transition: all 0.3s ease;
}

.music-reactive-text.music-active {
  animation: musicReactiveTextGlow 2.5s ease-in-out infinite;
}

/* Professional music-reactive page effects */
@keyframes musicReactivePageGlow {
  0%, 100% {
    box-shadow: inset 0 0 20px rgba(34, 197, 94, 0.1);
  }
  50% {
    box-shadow: inset 0 0 40px rgba(34, 197, 94, 0.2);
  }
}

@keyframes musicReactiveBackdrop {
  0%, 100% {
    backdrop-filter: blur(0px) saturate(1);
  }
  50% {
    backdrop-filter: blur(1px) saturate(1.1);
  }
}

.music-reactive-page {
  transition: all 0.3s ease;
}

.music-reactive-page.music-active {
  animation: musicReactivePageGlow 4s ease-in-out infinite;
}

/* Enhanced card effects */
.music-reactive-card.music-active {
  animation: musicReactiveScale 2s ease-in-out infinite, musicReactiveGlow 2s ease-in-out infinite;
  border: 1px solid rgba(34, 197, 94, 0.3);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(34, 197, 94, 0.05) 100%);
}

/* Smooth transitions for all music-reactive elements */
.music-reactive-float,
.music-reactive-rotate,
.music-reactive-scale,
.music-reactive-glow,
.music-reactive-color,
.music-reactive-border,
.music-reactive-text-glow {
  transition: all 0.3s ease;
}

/* Professional hover states */
.music-reactive-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.music-reactive-card.music-active:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(34, 197, 94, 0.2);
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Chrome mobile navbar fixes */
  .mobile-nav-container {
    -webkit-transform: translateZ(0); /* Force hardware acceleration */
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Ensure proper alignment in Chrome */
  .mobile-nav-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
  }

  /* Ensure animations work on mobile */
  .music-spin {
    animation: musicSpin 3s linear infinite; /* Same speed as desktop for consistency */
    transform-origin: center center;
    will-change: transform; /* Optimize for mobile performance */
  }

  .music-pulse {
    animation-duration: 3s; /* Slower pulse on mobile */
  }

  /* Optimize mobile music effects */
  .music-reactive-scale {
    animation-duration: 2.5s;
  }

  .music-reactive-float {
    animation-duration: 3s;
  }

  /* Mobile-friendly hover states */
  .music-reactive-card:hover {
    transform: none; /* Disable hover transforms on mobile */
  }

  /* Ensure mobile navigation music button is properly sized */
  .mobile-music-button {
    min-width: 40px;
    min-height: 40px;
    touch-action: manipulation; /* Optimize for touch */
    -webkit-tap-highlight-color: transparent; /* Remove Chrome tap highlight */
    -webkit-user-select: none;
    user-select: none;
  }

  /* Chrome-specific mobile navbar fixes */
  .mobile-nav-container {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Ensure proper spacing in Chrome mobile */
  .mobile-nav-flex > * {
    flex-shrink: 0;
  }

  /* Mobile-specific music status */
  .mobile-music-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Disable hover effects on touch devices */
  .music-reactive-card:hover,
  .music-reactive-card.music-active:hover {
    transform: none;
    box-shadow: none;
  }

  /* Optimize touch targets */
  button, .clickable {
    min-height: 44px; /* iOS recommended touch target size */
    min-width: 44px;
  }
}

/* Theme variables moved to :root */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

* {
  box-sizing: border-box;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Accessibility Enhancements */
.keyboard-navigation *:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

.reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.high-contrast {
  filter: contrast(150%);
}

.skip-link:focus {
  top: 6px !important;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for better accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-deep-charcoal {
    color: #000 !important;
  }

  .dark .text-dark-text {
    color: #fff !important;
  }

  .bg-accent-green {
    background-color: #00ff00 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
