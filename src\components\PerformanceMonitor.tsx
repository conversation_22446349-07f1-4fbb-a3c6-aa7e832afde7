"use client";

import { useEffect } from 'react';

export default function PerformanceMonitor() {
  useEffect(() => {
    // Performance monitoring
    const monitorPerformance = () => {
      if (typeof window === 'undefined') return;

      // Monitor page load performance
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          const paint = performance.getEntriesByType('paint');
          
          const metrics = {
            // Navigation timing
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            
            // Paint timing
            firstPaint: paint.find(entry => entry.name === 'first-paint')?.startTime || 0,
            firstContentfulPaint: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
            
            // Resource timing
            totalResources: performance.getEntriesByType('resource').length,
            
            // Memory usage (if available)
            memoryUsage: (performance as any).memory ? {
              used: (performance as any).memory.usedJSHeapSize,
              total: (performance as any).memory.totalJSHeapSize,
              limit: (performance as any).memory.jsHeapSizeLimit
            } : null
          };

          console.log('Performance Metrics:', metrics);
          
          // Send to analytics if available
          if (window.gtag) {
            window.gtag('event', 'page_performance', {
              event_category: 'Performance',
              dom_content_loaded: Math.round(metrics.domContentLoaded),
              load_complete: Math.round(metrics.loadComplete),
              first_paint: Math.round(metrics.firstPaint),
              first_contentful_paint: Math.round(metrics.firstContentfulPaint),
              total_resources: metrics.totalResources
            });
          }
        }, 1000);
      });

      // Monitor resource loading errors
      window.addEventListener('error', (event) => {
        if (event.target && event.target !== window) {
          const target = event.target as HTMLElement;
          console.error('Resource loading error:', {
            type: target.tagName,
            source: (target as any).src || (target as any).href,
            message: event.message
          });
          
          if (window.gtag) {
            window.gtag('event', 'resource_error', {
              event_category: 'Error',
              error_type: target.tagName,
              error_source: (target as any).src || (target as any).href
            });
          }
        }
      });

      // Monitor unhandled promise rejections
      window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
        
        if (window.gtag) {
          window.gtag('event', 'unhandled_rejection', {
            event_category: 'Error',
            error_message: event.reason?.toString() || 'Unknown error'
          });
        }
      });

      // Monitor long tasks (if supported)
      if ('PerformanceObserver' in window) {
        try {
          const longTaskObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              console.warn('Long task detected:', {
                duration: entry.duration,
                startTime: entry.startTime
              });
              
              if (window.gtag) {
                window.gtag('event', 'long_task', {
                  event_category: 'Performance',
                  task_duration: Math.round(entry.duration),
                  task_start_time: Math.round(entry.startTime)
                });
              }
            }
          });
          
          longTaskObserver.observe({ entryTypes: ['longtask'] });
        } catch (e) {
          console.log('Long task observer not supported');
        }

        // Monitor layout shifts
        try {
          const clsObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                console.log('Layout shift detected:', {
                  value: (entry as any).value,
                  startTime: entry.startTime
                });
              }
            }
          });
          
          clsObserver.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          console.log('Layout shift observer not supported');
        }
      }

      // Monitor connection quality
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        console.log('Connection info:', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData
        });

        connection.addEventListener('change', () => {
          console.log('Connection changed:', {
            effectiveType: connection.effectiveType,
            downlink: connection.downlink,
            rtt: connection.rtt
          });
          
          if (window.gtag) {
            window.gtag('event', 'connection_change', {
              event_category: 'Performance',
              effective_type: connection.effectiveType,
              downlink: connection.downlink,
              rtt: connection.rtt
            });
          }
        });
      }

      // Monitor visibility changes
      document.addEventListener('visibilitychange', () => {
        if (window.gtag) {
          window.gtag('event', 'visibility_change', {
            event_category: 'Engagement',
            visibility_state: document.visibilityState
          });
        }
      });
    };

    // Initialize monitoring
    monitorPerformance();

    // Cleanup function
    return () => {
      // Remove event listeners if needed
    };
  }, []);

  return null;
}
