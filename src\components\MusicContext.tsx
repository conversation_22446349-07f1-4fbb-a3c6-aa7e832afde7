"use client";

import { createContext, useContext, useState, useRef, ReactNode } from 'react';

interface MusicContextType {
  isPlaying: boolean;
  setIsPlaying: (playing: boolean) => void;
  currentTrack: string;
  setCurrentTrack: (track: string) => void;
  volume: number;
  setVolume: (volume: number) => void;
  audioRef: React.RefObject<HTMLAudioElement>;
  musicIntensity: number;
  setMusicIntensity: (intensity: number) => void;
}

const MusicContext = createContext<MusicContextType | undefined>(undefined);

export const useMusicContext = () => {
  const context = useContext(MusicContext);
  if (!context) {
    throw new Error('useMusicContext must be used within a MusicProvider');
  }
  return context;
};

interface MusicProviderProps {
  children: ReactNode;
}

export const MusicProvider = ({ children }: MusicProviderProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTrack, setCurrentTrack] = useState('');
  const [volume, setVolume] = useState(0.7);
  const [musicIntensity, setMusicIntensity] = useState(1);
  const audioRef = useRef<HTMLAudioElement>(null);

  const value = {
    isPlaying,
    setIsPlaying,
    currentTrack,
    setCurrentTrack,
    volume,
    setVolume,
    audioRef,
    musicIntensity,
    setMusicIntensity,
  };

  return (
    <MusicContext.Provider value={value}>
      {children}
      {/* Hidden audio element for music analysis */}
      <audio
        ref={audioRef}
        crossOrigin="anonymous"
        style={{ display: 'none' }}
      />
    </MusicContext.Provider>
  );
};
