import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import FloatingMusicButton from "@/components/FloatingMusicButton";
import YouTubeMusicPlayer from "@/components/YouTubeMusicPlayer";
import { MusicProvider } from "@/components/MusicContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: "Arkit Karmokar - Full Stack Developer & Software Engineer | arkit-k",
  description: "Arkit Karmokar (arkit-k) - Expert Full Stack Developer and Software Engineer. Specializing in React, Node.js, TypeScript. Available for hire at arkit-k.xyz",
  keywords: [
    // Primary target keywords - exact matches for search ranking
    "full stack developer",
    "software engineer",
    "arkit karmokar",
    "arkit-k",
    "Arkit Karmokar",
    "Arkit",
    // High-priority combinations with primary keywords
    "Arkit Karmokar full stack developer",
    "arkit-k software engineer",
    "Arkit full stack developer",
    "arkit karmokar software engineer",
    "full stack developer arkit",
    "software engineer arkit-k",
    "Arkit Karmokar software engineer",
    "arkit-k full stack developer",
    // Professional role variations
    "full stack web developer",
    "senior software engineer",
    "fullstack developer",
    "fullstack engineer",
    "web application developer",
    "react full stack developer",
    "node.js full stack developer",
    "typescript full stack developer",
    "javascript full stack developer",
    "python software engineer",
    "frontend backend developer",
    // Hiring and availability keywords
    "freelance full stack developer",
    "hire full stack developer",
    "full stack developer for hire",
    "software engineer for hire",
    "remote full stack developer",
    "contract software engineer",
    "freelance software engineer",
    // Portfolio and branding
    "arkit-k portfolio",
    "Arkit Karmokar portfolio",
    "full stack developer portfolio",
    "software engineer portfolio",
    "arkit portfolio",
    "full stack developer in mumbai",
    "full stack developer in india ",
    "Software engineer in india",
    


  ],
  authors: [{ name: "Arkit Karmokar", url: "https://arkit-k.xyz" }],
  creator: "Arkit Karmokar (arkit-k)",
  publisher: "Arkit Karmokar",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://arkit-k.xyz',
    siteName: 'Arkit Karmokar - Full Stack Developer Portfolio',
    title: 'Arkit Karmokar - Full Stack Developer & Software Engineer',
    description: 'Expert Full Stack Developer and Software Engineer specializing in modern web technologies. Available for hire.',
    images: [
      {
        url: 'https://arkit-k.xyz/hero.png',
        width: 1200,
        height: 630,
        alt: 'Arkit Karmokar - Full Stack Developer and Software Engineer',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Arkit Karmokar - Full Stack Developer & Software Engineer',
    description: 'Expert Full Stack Developer and Software Engineer. Explore my portfolio and projects.',
    images: ['https://arkit-k.xyz/hero.png'],
    creator: '@arkit_k',
  },
  alternates: {
    canonical: 'https://arkit-k.xyz',
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
    other: [
      {
        rel: 'android-chrome-192x192',
        url: '/android-chrome-192x192.png',
      },
      {
        rel: 'android-chrome-512x512',
        url: '/android-chrome-512x512.png',
      },
    ],
  },
  manifest: '/site.webmanifest',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Enhanced SEO Structured Data */}
        <script type="application/ld+json" suppressHydrationWarning dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Person",
            "name": "Arkit Karmokar",
            "alternateName": ["arkit", "arkit-k", "arkit karmokar"],
            "url": "https://arkit-k.xyz",
            "image": "https://arkit-k.xyz/hero.png",
            "jobTitle": ["Software Engineer", "Full Stack Developer", "Therapist", "Web Developer"],
            "worksFor": {
              "@type": "Organization",
              "name": "Freelance"
            },
            "knowsAbout": [
              "Software Engineering",
              "Full Stack Development",
              "Web Development",
              "React",
              "Next.js",
              "TypeScript",
              "JavaScript",
              "Python",
              "Node.js",
              "DevOps",
              "Frontend Development",
              "Backend Development"
            ],
            "description": "Software Engineer, Therapist, and creator specializing in modern web technologies.",
            "sameAs": [
              "https://github.com/arkit-k",
              "https://cal.com/arkit-karmokar-x0uyir/secret"
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "email": "<EMAIL>",
              "contactType": "professional"
            }
          })
        }} />

        {/* Website Structured Data */}
        <script type="application/ld+json" suppressHydrationWarning dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "Arkit Karmokar Portfolio",
            "alternateName": ["arkit portfolio", "arkit-k portfolio"],
            "url": "https://arkit-k.xyz",
            "description": "Professional portfolio of Arkit Karmokar (arkit-k), Expert Full Stack Developer and Software Engineer",
            "author": {
              "@type": "Person",
              "name": "Arkit Karmokar"
            },
            "potentialAction": {
              "@type": "SearchAction",
              "target": "https://arkit-k.xyz/?search={search_term_string}",
              "query-input": "required name=search_term_string"
            }
          })
        }} />

        {/* Professional Service Structured Data */}
        <script type="application/ld+json" suppressHydrationWarning dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ProfessionalService",
            "name": "Arkit Karmokar Software Development Services",
            "alternateName": ["arkit development", "arkit-k services"],
            "url": "https://arkit-k.xyz",
            "description": "Expert Full Stack Developer and Software Engineer services by Arkit Karmokar (arkit-k)",
            "provider": {
              "@type": "Person",
              "name": "Arkit Karmokar",
              "alternateName": ["arkit", "arkit-k"]
            },
            "areaServed": "Worldwide",
            "serviceType": [
              "Software Development",
              "Full Stack Development",
              "Web Development",
              "Frontend Development",
              "Backend Development",
              "DevOps Services"
            ]
          })
        }} />

        {/* Resource Hints for Performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        <link rel="dns-prefetch" href="//github.com" />
        <link rel="dns-prefetch" href="//avatars.githubusercontent.com" />
        <link rel="dns-prefetch" href="//www.youtube.com" />
        <link rel="dns-prefetch" href="//i.ytimg.com" />

        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Preload critical assets */}
        <link rel="preload" href="/tsar-3.png" as="image" type="image/png" />

        {/* Critical CSS optimizations */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical loading styles */
            .loading-skeleton {
              background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
              background-size: 200% 100%;
              animation: loading 1.5s infinite;
            }
            @keyframes loading {
              0% { background-position: 200% 0; }
              100% { background-position: -200% 0; }
            }
            /* Prevent layout shift */
            img { max-width: 100%; height: auto; }
            /* Focus indicators for accessibility */
            button:focus-visible, a:focus-visible {
              outline: 2px solid #3b82f6;
              outline-offset: 2px;
            }
            /* Reduced motion support */
            @media (prefers-reduced-motion: reduce) {
              *, *::before, *::after {
                animation-duration: 0.01ms !important;
                transition-duration: 0.01ms !important;
              }
            }
          `
        }} />

        {/* Simple client-side optimizations */}
        <script dangerouslySetInnerHTML={{
          __html: `
            // Simple performance monitoring
            if (typeof window !== 'undefined') {
              window.addEventListener('load', function() {
                // Basic performance logging
                setTimeout(function() {
                  if (performance && performance.getEntriesByType) {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    if (navigation) {
                      console.log('Page load time:', navigation.loadEventEnd - navigation.loadEventStart, 'ms');
                    }
                  }
                }, 1000);
              });

              // Service worker registration (production only)
              if ('serviceWorker' in navigator && location.hostname !== 'localhost') {
                navigator.serviceWorker.register('/sw.js').catch(function(e) {
                  console.log('SW registration failed');
                });
              }
            }
          `
        }} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <MusicProvider>
            {/* Optimization components temporarily disabled for SSR compatibility */}
            {/* <WebVitals /> */}
            {/* <CriticalCSS /> */}
            {/* <ServiceWorkerRegistration /> */}
            {/* <SEOOptimization /> */}
            {/* <AccessibilityEnhancements /> */}
            {/* <PerformanceMonitor /> */}
            {children}
            {/* Desktop Music Players */}
            <div className="hidden md:block">
              <FloatingMusicButton
                playlistId="37i9dQZF1DXcBWIGoYBM5M"
                playlistName="🎵 Arkit's Favorite Vibes"
                delay={3000}
              />
              <YouTubeMusicPlayer
                videoId="jfKfPfyJRdk"
                title="Arkit's Coding Vibes"
                autoShow={true}
              />
            </div>

            {/* Mobile Music Player - Integrated in MobileBottomNav */}
            <div className="md:hidden">
              {/* Mobile music is handled by MobileBottomNav component */}
            </div>

            {/* PWA Install Prompt - Temporarily disabled */}
            {/* <PWAInstallPrompt /> */}
          </MusicProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
