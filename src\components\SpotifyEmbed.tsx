"use client";

import { useState, useEffect } from 'react';
import { Music, Volume2, VolumeX, ExternalLink } from 'lucide-react';

interface SpotifyEmbedProps {
  playlistId: string;
  height?: number;
  showTitle?: boolean;
}

export default function SpotifyEmbed({ 
  playlistId, 
  height = 152,
  showTitle = true 
}: SpotifyEmbedProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  const spotifyEmbedUrl = `https://open.spotify.com/embed/playlist/${playlistId}?utm_source=generator&theme=0`;
  const spotifyDirectUrl = `https://open.spotify.com/playlist/${playlistId}`;

  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => setIsLoaded(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleOpenSpotify = () => {
    window.open(spotifyDirectUrl, '_blank');
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {showTitle && (
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Music className="w-5 h-5 text-green-500" />
            <h3 className="font-medium text-deep-charcoal dark:text-dark-text">
              My Coding Playlist
            </h3>
          </div>
          <button
            onClick={handleOpenSpotify}
            className="p-2 hover:bg-deep-charcoal/10 dark:hover:bg-dark-text/10 rounded-lg transition-colors"
            title="Open in Spotify"
          >
            <ExternalLink className="w-4 h-4 text-deep-charcoal/70 dark:text-dark-text/70" />
          </button>
        </div>
      )}

      <div className="relative bg-deep-charcoal/5 dark:bg-dark-text/5 rounded-xl overflow-hidden">
        {!isLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-deep-charcoal/5 dark:bg-dark-text/5">
            <div className="flex items-center space-x-2 text-deep-charcoal/50 dark:text-dark-text/50">
              <Music className="w-5 h-5 animate-pulse" />
              <span className="text-sm">Loading playlist...</span>
            </div>
          </div>
        )}
        
        <iframe
          src={spotifyEmbedUrl}
          width="100%"
          height={height}
          frameBorder="0"
          allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
          loading="lazy"
          onLoad={() => setIsLoaded(true)}
          className={`transition-opacity duration-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        />
      </div>

      <div className="mt-2 text-center">
        <p className="text-xs text-deep-charcoal/50 dark:text-dark-text/50">
          🎵 Perfect vibes for coding and browsing
        </p>
      </div>
    </div>
  );
}
