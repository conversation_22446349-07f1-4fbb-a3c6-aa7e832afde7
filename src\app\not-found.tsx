import React from 'react'
import Link from 'next/link'
import Image from 'next/image'

const NotFound = () => {
  return (
    <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors duration-300 flex items-center">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center justify-between gap-12">
          {/* Left Column - Image */}
          <div className="w-full md:w-1/2 flex justify-center">
            <div className="relative w-full max-w-md aspect-square">
              <Image
                src="/404-2.png" // Replace with your image path
                alt="404 Illustration"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="w-full md:w-1/2">
            <div className="max-w-md">
              {/* Logo */}
              <div className="mb-8">
                <div className="w-16 h-16 relative">
                  <Image
                    src="/arkit-logo.png"
                    alt="Arkit_k Logo"
                    width={64}
                    height={64}
                    className="object-contain rounded-full"
                  />
                </div>
              </div>

              {/* 404 Number */}
              <h1 className="text-8xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                404
              </h1>

              {/* Title */}
              <h2 className="text-3xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                Oops! Page Not Found
              </h2>

              {/* Description */}
              <p className="text-lg text-deep-charcoal/80 dark:text-dark-text/70 mb-8">
                The page you're looking for doesn't exist or has been moved.
              </p>

              {/* Back Button */}
              <Link
                href="/"
              className="inline-block px-8 py-3 border border-light-almond/30 dark:border-dark-text/30 text-light-almond dark:text-dark-text hover:border-accent-green hover:text-accent-green font-medium rounded-lg transition-colors text-center"
              >
                Return to Homepage
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotFound