"use client";

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface MotionWrapperProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  type?: 'fade' | 'slide' | 'scale' | 'rotate';
}

const variants = {
  fade: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  },
  slide: {
    up: {
      hidden: { opacity: 0, y: 50 },
      visible: { opacity: 1, y: 0 }
    },
    down: {
      hidden: { opacity: 0, y: -50 },
      visible: { opacity: 1, y: 0 }
    },
    left: {
      hidden: { opacity: 0, x: 50 },
      visible: { opacity: 1, x: 0 }
    },
    right: {
      hidden: { opacity: 0, x: -50 },
      visible: { opacity: 1, x: 0 }
    }
  },
  scale: {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 }
  },
  rotate: {
    hidden: { opacity: 0, rotate: -10 },
    visible: { opacity: 1, rotate: 0 }
  }
};

export default function MotionWrapper({
  children,
  className = '',
  delay = 0,
  duration = 0.6,
  direction = 'up',
  type = 'slide'
}: MotionWrapperProps) {
  const getVariant = () => {
    switch (type) {
      case 'fade':
        return variants.fade;
      case 'slide':
        return variants.slide[direction as keyof typeof variants.slide];
      case 'scale':
        return variants.scale;
      case 'rotate':
        return variants.rotate;
      default:
        return variants.slide.up;
    }
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      transition={{
        duration,
        delay,
        ease: [0.25, 0.25, 0.25, 0.75]
      }}
      variants={getVariant()}
    >
      {children}
    </motion.div>
  );
}

// Specialized components for common use cases
export function FadeIn({ children, className = '', delay = 0 }: { children: ReactNode; className?: string; delay?: number }) {
  return (
    <MotionWrapper type="fade" className={className} delay={delay}>
      {children}
    </MotionWrapper>
  );
}

export function SlideUp({ children, className = '', delay = 0 }: { children: ReactNode; className?: string; delay?: number }) {
  return (
    <MotionWrapper type="slide" direction="up" className={className} delay={delay}>
      {children}
    </MotionWrapper>
  );
}

export function SlideLeft({ children, className = '', delay = 0 }: { children: ReactNode; className?: string; delay?: number }) {
  return (
    <MotionWrapper type="slide" direction="left" className={className} delay={delay}>
      {children}
    </MotionWrapper>
  );
}

export function ScaleIn({ children, className = '', delay = 0 }: { children: ReactNode; className?: string; delay?: number }) {
  return (
    <MotionWrapper type="scale" className={className} delay={delay}>
      {children}
    </MotionWrapper>
  );
}
