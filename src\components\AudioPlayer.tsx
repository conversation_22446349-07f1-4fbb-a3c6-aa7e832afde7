"use client";

import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Ski<PERSON>For<PERSON>, SkipBack, Music } from 'lucide-react';

interface Track {
  id: number;
  title: string;
  artist: string;
  src: string;
}

interface AudioPlayerProps {
  tracks: Track[];
  autoShow?: boolean;
  volume?: number;
}

const defaultTracks: Track[] = [
  {
    id: 1,
    title: "Coding Flow",
    artist: "Arkit's Mix",
    src: "/audio/track1.mp3" // You'll need to add audio files to public/audio/
  },
  {
    id: 2,
    title: "Focus Mode",
    artist: "Arkit's Mix", 
    src: "/audio/track2.mp3"
  },
  {
    id: 3,
    title: "Deep Work",
    artist: "Arkit's Mix",
    src: "/audio/track3.mp3"
  }
];

export default function AudioPlayer({ 
  tracks = defaultTracks,
  autoShow = true,
  volume = 0.3 
}: AudioPlayerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTrackIndex, setCurrentTrackIndex] = useState(0);
  const [currentVolume, setCurrentVolume] = useState(volume);
  const [isMuted, setIsMuted] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const currentTrack = tracks[currentTrackIndex];

  useEffect(() => {
    if (autoShow) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [autoShow]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : currentVolume;
    }
  }, [currentVolume, isMuted]);

  const handlePlay = async () => {
    if (!hasUserInteracted) {
      setHasUserInteracted(true);
    }

    if (audioRef.current) {
      try {
        if (isPlaying) {
          audioRef.current.pause();
          setIsPlaying(false);
        } else {
          await audioRef.current.play();
          setIsPlaying(true);
        }
      } catch (error) {
        console.log('Audio play failed:', error);
      }
    }
  };

  const handleNext = () => {
    const nextIndex = (currentTrackIndex + 1) % tracks.length;
    setCurrentTrackIndex(nextIndex);
    if (isPlaying && audioRef.current) {
      setTimeout(() => {
        audioRef.current?.play();
      }, 100);
    }
  };

  const handlePrevious = () => {
    const prevIndex = currentTrackIndex === 0 ? tracks.length - 1 : currentTrackIndex - 1;
    setCurrentTrackIndex(prevIndex);
    if (isPlaying && audioRef.current) {
      setTimeout(() => {
        audioRef.current?.play();
      }, 100);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setCurrentVolume(newVolume);
    if (newVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  };

  const handleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    if (isPlaying && audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleTrackEnd = () => {
    handleNext(); // Auto-play next track
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-6 left-6 z-40 animate-in slide-in-from-left-2 duration-700">
      <div className="bg-white dark:bg-dark-surface rounded-xl shadow-lg border border-deep-charcoal/10 dark:border-dark-text/10 p-4 max-w-sm">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
              <Music className="w-4 h-4 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-deep-charcoal dark:text-dark-text text-sm truncate">
                {currentTrack.title}
              </h3>
              <p className="text-xs text-deep-charcoal/60 dark:text-dark-text/60 truncate">
                {currentTrack.artist}
              </p>
            </div>
          </div>
          <button
            onClick={handleDismiss}
            className="text-deep-charcoal/50 dark:text-dark-text/50 hover:text-deep-charcoal dark:hover:text-dark-text transition-colors text-sm"
          >
            ✕
          </button>
        </div>

        {/* Player Controls */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-1">
            <button
              onClick={handlePrevious}
              className="p-2 text-deep-charcoal/70 dark:text-dark-text/70 hover:text-deep-charcoal dark:hover:text-dark-text transition-colors"
            >
              <SkipBack className="w-4 h-4" />
            </button>
            <button
              onClick={handlePlay}
              className="w-10 h-10 bg-purple-500 hover:bg-purple-600 text-white rounded-full flex items-center justify-center transition-colors"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4 ml-0.5" />}
            </button>
            <button
              onClick={handleNext}
              className="p-2 text-deep-charcoal/70 dark:text-dark-text/70 hover:text-deep-charcoal dark:hover:text-dark-text transition-colors"
            >
              <SkipForward className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleMute}
              className="p-1 text-deep-charcoal/70 dark:text-dark-text/70 hover:text-deep-charcoal dark:hover:text-dark-text transition-colors"
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </button>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={currentVolume}
              onChange={handleVolumeChange}
              className="w-16 h-1 bg-deep-charcoal/20 dark:bg-dark-text/20 rounded-lg appearance-none cursor-pointer"
            />
          </div>
        </div>

        {/* Track Info */}
        <div className="text-center">
          <p className="text-xs text-deep-charcoal/50 dark:text-dark-text/50">
            Track {currentTrackIndex + 1} of {tracks.length} • 
            {isPlaying ? ' 🎵 Playing' : ' ⏸️ Paused'}
          </p>
        </div>

        {/* Hidden Audio Element */}
        <audio
          ref={audioRef}
          src={currentTrack.src}
          onEnded={handleTrackEnd}
          preload="metadata"
        />
      </div>
    </div>
  );
}
