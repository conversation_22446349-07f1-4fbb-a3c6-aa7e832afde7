"use client";

import { useEffect } from 'react';
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals';

export default function WebVitals() {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return;

    // Core Web Vitals
    onCLS((metric) => {
      console.log('CLS:', metric);
      // Send to analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'web_vitals', {
          event_category: 'Web Vitals',
          event_label: metric.name,
          value: Math.round(metric.value * 1000),
          custom_map: { metric_id: 'cls' },
        });
      }
    });

    onINP((metric) => {
      console.log('INP:', metric);
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'web_vitals', {
          event_category: 'Web Vitals',
          event_label: metric.name,
          value: Math.round(metric.value),
          custom_map: { metric_id: 'inp' },
        });
      }
    });

    onLCP((metric) => {
      console.log('LCP:', metric);
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'web_vitals', {
          event_category: 'Web Vitals',
          event_label: metric.name,
          value: Math.round(metric.value),
          custom_map: { metric_id: 'lcp' },
        });
      }
    });

    // Additional metrics
    onFCP((metric) => {
      console.log('FCP:', metric);
    });

    onTTFB((metric) => {
      console.log('TTFB:', metric);
    });
  }, []);

  return null;
}

// Type declaration for gtag
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}
