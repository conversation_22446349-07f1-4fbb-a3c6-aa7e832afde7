import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Terms of Service - Arkit Karmokar | arkit-k.xyz',
  description: 'Terms of Service for Arkit Karmokar (arkit-k) portfolio website and services.',
  robots: {
    index: true,
    follow: true,
  },
}

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-light-almond dark:bg-dark-bg transition-colors">
      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-12">
          <Link 
            href="/"
            className="inline-flex items-center text-deep-charcoal dark:text-dark-text hover:text-accent-green transition-colors mb-8"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Portfolio
          </Link>
          
          <h1 className="text-4xl lg:text-5xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
            Terms of Service
          </h1>
          <p className="text-lg text-deep-charcoal/80 dark:text-dark-text/80">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        {/* Content */}
        <div className="prose prose-lg max-w-none text-deep-charcoal dark:text-dark-text">
          <div className="space-y-8">
            
            <section>
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                1. Acceptance of Terms
              </h2>
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
                By accessing and using this website (arkit-k.xyz), you accept and agree to be bound by the terms and provision of this agreement. 
                If you do not agree to abide by the above, please do not use this service.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                2. Use License
              </h2>
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed mb-4">
                Permission is granted to temporarily download one copy of the materials on Arkit Karmokar's website for personal, 
                non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
              </p>
              <ul className="list-disc list-inside text-deep-charcoal/80 dark:text-dark-text/80 space-y-2">
                <li>modify or copy the materials</li>
                <li>use the materials for any commercial purpose or for any public display</li>
                <li>attempt to reverse engineer any software contained on the website</li>
                <li>remove any copyright or other proprietary notations from the materials</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                3. Disclaimer
              </h2>
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
                The materials on Arkit Karmokar's website are provided on an 'as is' basis. Arkit Karmokar makes no warranties, 
                expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties 
                or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                4. Limitations
              </h2>
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
                In no event shall Arkit Karmokar or its suppliers be liable for any damages (including, without limitation, damages for loss of data 
                or profit, or due to business interruption) arising out of the use or inability to use the materials on Arkit Karmokar's website, 
                even if Arkit Karmokar or an authorized representative has been notified orally or in writing of the possibility of such damage.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                5. Professional Services
              </h2>
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
                Any professional services offered through this website are subject to separate agreements and terms. 
                Contact information and service details are provided for informational purposes only.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                6. Revisions and Errata
              </h2>
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
                The materials appearing on Arkit Karmokar's website could include technical, typographical, or photographic errors. 
                Arkit Karmokar does not warrant that any of the materials on its website are accurate, complete, or current.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
                7. Contact Information
              </h2>
              <p className="text-deep-charcoal/80 dark:text-dark-text/80 leading-relaxed">
                If you have any questions about these Terms of Service, please contact:
              </p>
              <div className="mt-4 p-4 bg-deep-charcoal/10 dark:bg-dark-text/10 rounded-lg">
                <p className="text-deep-charcoal dark:text-dark-text font-medium">
                  Arkit Karmokar<br />
                  Email: <EMAIL><br />
                  Website: arkit-k.xyz
                </p>
              </div>
            </section>

          </div>
        </div>
      </div>
    </div>
  )
}
