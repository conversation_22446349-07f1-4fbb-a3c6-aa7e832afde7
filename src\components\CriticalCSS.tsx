"use client";

import { useEffect } from 'react';

export default function CriticalCSS() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Preload critical fonts
    const preloadFont = (href: string, type: string = 'font/woff2') => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = href;
      link.as = 'font';
      link.type = type;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    };

    // Preload critical resources
    const preloadResource = (href: string, as: string) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = href;
      link.as = as;
      document.head.appendChild(link);
    };

    // DNS prefetch for external domains
    const dnsPrefetch = (domain: string) => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    };

    // Preconnect to external domains
    const preconnect = (domain: string) => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    };

    // Critical optimizations
    dnsPrefetch('//fonts.googleapis.com');
    dnsPrefetch('//fonts.gstatic.com');
    dnsPrefetch('//github.com');
    dnsPrefetch('//avatars.githubusercontent.com');
    dnsPrefetch('//www.youtube.com');
    dnsPrefetch('//i.ytimg.com');

    preconnect('https://fonts.googleapis.com');
    preconnect('https://fonts.gstatic.com');

    // Defer non-critical CSS
    const deferCSS = () => {
      const links = document.querySelectorAll('link[rel="stylesheet"]');
      links.forEach((link) => {
        if (!link.hasAttribute('data-critical')) {
          link.setAttribute('media', 'print');
          link.addEventListener('load', () => {
            link.setAttribute('media', 'all');
          });
        }
      });
    };

    // Run after initial render
    setTimeout(deferCSS, 100);

    // Cleanup function
    return () => {
      // Remove preload links after they're no longer needed
      const preloadLinks = document.querySelectorAll('link[rel="preload"]');
      preloadLinks.forEach((link) => {
        if (link.hasAttribute('data-cleanup')) {
          link.remove();
        }
      });
    };
  }, []);

  return null;
}
