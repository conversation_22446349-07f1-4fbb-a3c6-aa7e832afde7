import dynamic from 'next/dynamic';

// Lazy load heavy components with loading states
export const DynamicFloatingMusicButton = dynamic(
  () => import('@/components/FloatingMusicButton'),
  {
    loading: () => <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />,
    ssr: false, // Music player doesn't need SSR
  }
);

export const DynamicYouTubeMusicPlayer = dynamic(
  () => import('@/components/YouTubeMusicPlayer'),
  {
    loading: () => <div className="w-80 h-20 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />,
    ssr: false, // YouTube player doesn't need SSR
  }
);

export const DynamicMobileBottomNav = dynamic(
  () => import('@/components/MobileBottomNav'),
  {
    loading: () => (
      <div className="fixed bottom-0 left-0 right-0 h-16 bg-white/80 dark:bg-dark-surface/80 backdrop-blur-md border-t border-deep-charcoal/10 dark:border-dark-text/10 md:hidden z-50">
        <div className="flex items-center justify-center h-full">
          <div className="flex space-x-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    ),
    ssr: false, // Mobile nav has client-side interactions
  }
);

export const DynamicContributionSection = dynamic(
  () => import('@/components/sections/ContributionSection'),
  {
    loading: () => (
      <section className="px-6 lg:px-12 mt-24">
        <div className="max-w-7xl mx-auto">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4" />
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-8 w-2/3" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            ))}
          </div>
        </div>
      </section>
    ),
  }
);

// Utility function for lazy loading with intersection observer
export const createLazyComponent = <T extends Record<string, any>>(
  importFn: () => Promise<{ default: React.ComponentType<T> }>,
  fallback?: React.ComponentType
) => {
  return dynamic(importFn, {
    loading: fallback ? () => React.createElement(fallback) : undefined,
    ssr: true,
  });
};

// Preload functions for critical components
export const preloadCriticalComponents = () => {
  // Preload components that will be needed soon
  import('@/components/sections/AboutSection');
  import('@/components/sections/ProjectSection');
};

// Prefetch non-critical components
export const prefetchNonCriticalComponents = () => {
  // Prefetch components that might be needed later
  setTimeout(() => {
    import('@/components/sections/WorkExperienceSection');
    import('@/components/sections/HireMeSection');
    import('@/components/sections/ContributionSection');
  }, 2000);
};
