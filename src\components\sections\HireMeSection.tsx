"use client";

import Image from "next/image";
import Link from "next/link"

export default function HireMeSection() {
  return (
    <section id="hire" className="px-6 lg:px-12 mt-16 pt-8 border-t border-light-almond/10 dark:border-dark-text/10">
      <div className="max-w-7xl mx-auto">
        <div className="bg-deep-charcoal dark:bg-dark-surface rounded-2xl p-8 lg:p-12">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left - Character Image */}
          <div className="flex justify-center lg:justify-start animate-scale-in">
            <div className="w-64 h-80 lg:w-80 lg:h-96 relative parallax-element">
              <div className="w-full h-full bg-gradient-to-br from-accent-green/20 to-accent-green/10 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <Image
                    src="/ichino.png"
                    alt="Character illustration showing a person with glasses making a peace sign"
                    fill
                    className="object-contain"
                    priority
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Right - Content */}
          <div className="space-y-6">
            <h2 className="text-3xl lg:text-4xl font-bold text-light-almond dark:text-dark-text animate-fade-in">
              Why should you hire me?
            </h2>

            <div className="space-y-6 text-light-almond/80 dark:text-dark-text/80 animate-slide-up">
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text mb-2">
                      Full-Stack Expertise
                    </h3>
                    <p className="text-sm leading-relaxed">
                      I bring end-to-end development skills with modern technologies like React, Next.js,
                      Node.js, and cloud platforms. I can handle everything from UI/UX to database design
                      and deployment.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                  
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center  flex-shrink-0">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text mb-2">
                      Problem-Solving Mindset
                    </h3>
                    <p className="text-sm leading-relaxed">
                      I don't just write code—I solve business problems. I take time to understand
                      requirements, propose solutions, and deliver products that users actually want to use.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 bg-accent-green rounded-full flex items-center justify-center mt-1 flex-shrink-0">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-light-almond dark:text-dark-text mb-2">
                      Continuous Learning
                    </h3>
                    <p className="text-sm leading-relaxed">
                      Technology evolves fast, and so do I. I stay updated with the latest trends,
                      best practices, and tools to ensure I'm always bringing fresh ideas and
                      efficient solutions to the table.
                    </p>
                  </div>
                </div>
              </div>

              {/* Call to Action */}
              <div className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <button className="px-8 py-3 bg-accent-green hover:bg-accent-green/80  dark:text-dark-text font-medium rounded-lg transition-colors">
                   <Link href={"https://cal.com/arkit-karmokar-x0uyir/secret"}>Let's Work Together</Link>
                  </button>
                  <Link
                    href="https://drive.google.com/file/d/18jaJPxTYUVgLgRrrkkz-Mz8-cW6fAxnr/view"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block px-8 py-3 border border-light-almond/30 dark:border-dark-text/30 text-light-almond dark:text-dark-text hover:border-accent-green hover:text-accent-green font-medium rounded-lg transition-colors text-center"
                  >
                    View My Resume
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}
