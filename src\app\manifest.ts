import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Arkit Karmokar - Full Stack Developer Portfolio',
    short_name: 'Arkit Portfolio',
    description: 'Arkit Karmokar (arkit-k) - Expert Full Stack Developer and Software Engineer portfolio',
    start_url: '/',
    display: 'standalone',
    background_color: '#f5f5dc',
    theme_color: '#22c55e',
    orientation: 'portrait-primary',
    scope: '/',
    lang: 'en',
    categories: ['business', 'productivity', 'developer'],
    icons: [
      {
        src: '/tsar-3.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'maskable'
      },
      {
        src: '/tsar-3.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'any'
      }
    ],
    screenshots: [
      {
        src: '/screenshot-desktop.png',
        sizes: '1280x720',
        type: 'image/png',
        form_factor: 'wide',
        label: 'Desktop view of Arkit Karmokar portfolio'
      },
      {
        src: '/screenshot-mobile.png',
        sizes: '390x844',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'Mobile view of Arkit Karmokar portfolio'
      }
    ],
    shortcuts: [
      {
        name: 'About',
        short_name: 'About',
        description: 'Learn about Arkit Karmokar',
        url: '/#about',
        icons: [{ src: '/tsar-3.png', sizes: '96x96' }]
      },
      {
        name: 'Projects',
        short_name: 'Projects',
        description: 'View portfolio projects',
        url: '/#projects',
        icons: [{ src: '/tsar-3.png', sizes: '96x96' }]
      },
      {
        name: 'Hire Me',
        short_name: 'Hire',
        description: 'Get in touch for work',
        url: '/#hire',
        icons: [{ src: '/tsar-3.png', sizes: '96x96' }]
      }
    ],
    related_applications: [
      {
        platform: 'web',
        url: 'https://arkit-k.xyz'
      }
    ],
    prefer_related_applications: false
  }
}
