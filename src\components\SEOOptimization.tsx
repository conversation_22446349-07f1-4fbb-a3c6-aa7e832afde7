"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

export default function SEOOptimization() {
  const pathname = usePathname();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Dynamic meta tag updates based on section
    const updateMetaTags = () => {
      const hash = window.location.hash;
      let title = 'Arkit Karmokar - Full Stack Developer & Software Engineer | arkit-k';
      let description = 'Arkit Karmokar (arkit-k) - Expert Full Stack Developer and Software Engineer. Specializing in React, Node.js, TypeScript. Available for hire at arkit-k.xyz';

      switch (hash) {
        case '#about':
          title = 'About Arkit Karmokar | Full Stack Developer arkit-k';
          description = 'Learn about <PERSON><PERSON> Karmokar (arkit-k), an experienced Full Stack Developer and Software Engineer with expertise in modern web technologies.';
          break;
        case '#projects':
          title = 'Projects by Arkit Karmokar | arkit-k Portfolio';
          description = 'Explore innovative projects by Arkit Karmokar (arkit-k) including <PERSON><PERSON><PERSON>-a<PERSON>, Designers, and other full stack development work.';
          break;
        case '#work-experience':
          title = 'Work Experience - Arkit Karmokar | Software Engineer arkit-k';
          description = 'Professional work experience of Arkit Karmokar (arkit-k) as a Full Stack Developer and Software Engineer.';
          break;
        case '#hire':
          title = 'Hire Arkit Karmokar | Full Stack Developer arkit-k Available';
          description = 'Hire Arkit Karmokar (arkit-k) for your next project. Expert Full Stack Developer and Software Engineer available for freelance work.';
          break;
        case '#contributions':
          title = 'Open Source Contributions | Arkit Karmokar arkit-k GitHub';
          description = 'View open source contributions and GitHub activity by Arkit Karmokar (arkit-k), showcasing commitment to the developer community.';
          break;
      }

      // Update document title
      document.title = title;

      // Update meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', description);
      }

      // Update Open Graph tags
      const ogTitle = document.querySelector('meta[property="og:title"]');
      if (ogTitle) {
        ogTitle.setAttribute('content', title);
      }

      const ogDescription = document.querySelector('meta[property="og:description"]');
      if (ogDescription) {
        ogDescription.setAttribute('content', description);
      }

      // Update Twitter Card tags
      const twitterTitle = document.querySelector('meta[name="twitter:title"]');
      if (twitterTitle) {
        twitterTitle.setAttribute('content', title);
      }

      const twitterDescription = document.querySelector('meta[name="twitter:description"]');
      if (twitterDescription) {
        twitterDescription.setAttribute('content', description);
      }

      // Update canonical URL
      const canonical = document.querySelector('link[rel="canonical"]');
      if (canonical) {
        canonical.setAttribute('href', `https://arkit-k.xyz${pathname}${hash}`);
      }
    };

    // Update on hash change
    const handleHashChange = () => {
      updateMetaTags();
      
      // Track page views for analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('config', 'GA_MEASUREMENT_ID', {
          page_title: document.title,
          page_location: window.location.href,
        });
      }
    };

    // Initial update
    updateMetaTags();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);

    // Cleanup
    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [pathname]);

  useEffect(() => {
    // Add structured data for current page
    const addStructuredData = () => {
      const existingScript = document.getElementById('dynamic-structured-data');
      if (existingScript) {
        existingScript.remove();
      }

      const script = document.createElement('script');
      script.id = 'dynamic-structured-data';
      script.type = 'application/ld+json';
      
      const structuredData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": document.title,
        "description": document.querySelector('meta[name="description"]')?.getAttribute('content'),
        "url": window.location.href,
        "mainEntity": {
          "@type": "Person",
          "name": "Arkit Karmokar",
          "alternateName": ["arkit", "arkit-k"],
          "jobTitle": "Full Stack Developer",
          "description": "Expert Full Stack Developer and Software Engineer",
          "url": "https://arkit-k.xyz",
          "sameAs": [
            "https://github.com/Arkit-k",
            "https://linkedin.com/in/arkit-karmokar"
          ]
        }
      };

      script.textContent = JSON.stringify(structuredData);
      document.head.appendChild(script);
    };

    // Add structured data after component mounts
    setTimeout(addStructuredData, 100);
  }, []);

  return null;
}
