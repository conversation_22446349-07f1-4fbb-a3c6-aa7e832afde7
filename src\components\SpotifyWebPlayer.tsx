"use client";

import { useState, useEffect, useRef } from 'react';
import { Play, Pause, SkipForward, SkipBack, Volume2 } from 'lucide-react';

declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void;
    Spotify: any;
  }
}

interface SpotifyWebPlayerProps {
  clientId: string;
  playlistUri?: string;
}

export default function SpotifyWebPlayer({ 
  clientId,
  playlistUri = "spotify:playlist:10M75TUt3X1qbBhpuEw6el"
}: SpotifyWebPlayerProps) {
  const [isReady, setIsReady] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [player, setPlayer] = useState<any>(null);
  const [currentTrack, setCurrentTrack] = useState<any>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [deviceId, setDeviceId] = useState<string>('');
  const [accessToken, setAccessToken] = useState<string>('');

  // Load Spotify Web Playback SDK
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://sdk.scdn.co/spotify-player.js';
    script.async = true;
    document.body.appendChild(script);

    window.onSpotifyWebPlaybackSDKReady = () => {
      setIsReady(true);
    };

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  // Initialize player when SDK is ready
  useEffect(() => {
    if (!isReady || !accessToken) return;

    const spotifyPlayer = new window.Spotify.Player({
      name: 'Arkit Portfolio Player',
      getOAuthToken: (cb: (token: string) => void) => {
        cb(accessToken);
      },
      volume: 0.5
    });

    // Error handling
    spotifyPlayer.addListener('initialization_error', ({ message }: any) => {
      console.error('Failed to initialize:', message);
    });

    spotifyPlayer.addListener('authentication_error', ({ message }: any) => {
      console.error('Failed to authenticate:', message);
    });

    spotifyPlayer.addListener('account_error', ({ message }: any) => {
      console.error('Failed to validate Spotify account:', message);
    });

    // Playback status updates
    spotifyPlayer.addListener('player_state_changed', (state: any) => {
      if (!state) return;

      setCurrentTrack(state.track_window.current_track);
      setIsPlaying(!state.paused);
    });

    // Ready
    spotifyPlayer.addListener('ready', ({ device_id }: any) => {
      console.log('Ready with Device ID', device_id);
      setDeviceId(device_id);
      setIsActive(true);
    });

    // Connect to the player
    spotifyPlayer.connect();
    setPlayer(spotifyPlayer);

    return () => {
      spotifyPlayer.disconnect();
    };
  }, [isReady, accessToken]);

  const handleLogin = async () => {
    const scopes = [
      'streaming',
      'user-read-email',
      'user-read-private',
      'user-read-playback-state',
      'user-modify-playback-state'
    ];

    const authUrl = `https://accounts.spotify.com/authorize?` +
      `client_id=${clientId}&` +
      `response_type=token&` +
      `redirect_uri=${encodeURIComponent(window.location.origin)}&` +
      `scope=${encodeURIComponent(scopes.join(' '))}`;

    window.location.href = authUrl;
  };

  const handlePlay = () => {
    if (player) {
      player.togglePlay();
    }
  };

  const handleNext = () => {
    if (player) {
      player.nextTrack();
    }
  };

  const handlePrevious = () => {
    if (player) {
      player.previousTrack();
    }
  };

  // Extract access token from URL hash (after Spotify redirect)
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const token = hash.split('&')[0].split('=')[1];
      if (token) {
        setAccessToken(token);
        window.location.hash = ''; // Clean URL
      }
    }
  }, []);

  if (!accessToken) {
    return (
      <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-xl shadow-lg">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">🎵 Listen to My Playlist</h3>
          <p className="text-sm text-green-100 mb-4">
            Connect your Spotify Premium account to play music directly here!
          </p>
          <button
            onClick={handleLogin}
            className="bg-white text-green-600 font-medium px-6 py-2 rounded-lg hover:bg-green-50 transition-colors"
          >
            Connect Spotify
          </button>
        </div>
      </div>
    );
  }

  if (!isActive) {
    return (
      <div className="bg-deep-charcoal/5 dark:bg-dark-text/5 p-6 rounded-xl">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-green-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
            Connecting to Spotify...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl shadow-lg">
      {currentTrack && (
        <div className="flex items-center space-x-4">
          <img
            src={currentTrack.album.images[0]?.url}
            alt={currentTrack.album.name}
            className="w-12 h-12 rounded-lg"
          />
          <div className="flex-1 min-w-0">
            <p className="font-medium truncate">{currentTrack.name}</p>
            <p className="text-sm text-green-100 truncate">
              {currentTrack.artists.map((artist: any) => artist.name).join(', ')}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrevious}
              className="p-2 hover:bg-white/10 rounded-full transition-colors"
            >
              <SkipBack className="w-4 h-4" />
            </button>
            <button
              onClick={handlePlay}
              className="p-2 hover:bg-white/10 rounded-full transition-colors"
            >
              {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
            </button>
            <button
              onClick={handleNext}
              className="p-2 hover:bg-white/10 rounded-full transition-colors"
            >
              <SkipForward className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
